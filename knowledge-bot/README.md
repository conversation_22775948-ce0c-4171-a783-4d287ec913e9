# Knowledge Bot - FastAPI Microservice

A robust FastAPI microservice for chatbot functionality with RAG (Retrieval-Augmented Generation) system and comprehensive FAQ management. This microservice is designed to work with a main NestJS application that handles user management and authentication.

## Features

### 🤖 RAG System
- **Vector Search**: Semantic search using OpenAI embeddings and pgvector
- **Question Answering**: Intelligent responses based on FAQ knowledge base
- **Similarity Matching**: Find similar FAQs using vector similarity
- **Confidence Scoring**: Response confidence based on retrieval quality

### 📚 FAQ Management
- **Full CRUD Operations**: Create, Read, Update, Delete FAQs
- **Categorization**: Organize FAQs by categories
- **Tagging System**: Tag FAQs for better organization
- **Priority System**: Set FAQ priority levels
- **View Tracking**: Track FAQ popularity
- **Bulk Operations**: Create multiple FAQs at once

### 🔐 Authentication & Security
- **API Integration**: Makes comprehensive API calls to main NestJS application
- **User Validation**: Validates users by calling main app endpoints
- **Permission Checking**: Real-time permission and role verification
- **Activity Logging**: Logs all user activities to main application
- **Anonymous User Support**: Limited functionality for unauthenticated users
- **Optional API Key Protection**: Simple protection for direct access

### 🗄️ Database
- **PostgreSQL**: Robust relational database
- **Vector Storage**: pgvector extension for embeddings
- **Migrations**: Alembic for database schema management
- **Connection Pooling**: Efficient database connections

### 🚀 API Features
- **OpenAPI Documentation**: Auto-generated API docs
- **WebSocket Support**: Real-time chat functionality
- **Error Handling**: Comprehensive error responses
- **Health Checks**: System health monitoring
- **CORS Support**: Cross-origin resource sharing

## Architecture

```
knowledge-bot/
├── src/
│   ├── auth/                 # JWT token validation
│   ├── DAO/                  # Data Access Objects
│   │   └── faqDAO.py
│   ├── Faqs/                 # FAQ management
│   │   ├── router.py
│   │   └── service.py
│   ├── RagChain/             # RAG system
│   │   ├── router.py
│   │   ├── service.py
│   │   ├── rag_service.py
│   │   └── websockets.py
│   ├── database.py           # Database configuration
│   ├── databaseModels.py     # SQLAlchemy models
│   ├── schemas.py            # Pydantic schemas
│   └── main.py               # FastAPI application
├── alembic/                  # Database migrations
├── requirements.txt          # Python dependencies
└── .env                      # Environment variables
```

## Setup Instructions

### Prerequisites

1. **Python 3.9+**
2. **PostgreSQL 12+** with pgvector extension
3. **OpenAI API Key** (for embeddings and chat)

### 1. Database Setup

```bash
# Install PostgreSQL and pgvector extension
# For Ubuntu/Debian:
sudo apt-get install postgresql postgresql-contrib
sudo apt-get install postgresql-12-pgvector

# For macOS with Homebrew:
brew install postgresql
brew install pgvector

# Create database and user
sudo -u postgres psql
CREATE DATABASE knowledge_graph;
CREATE USER cspro_admin WITH PASSWORD 'super-secret-123';
GRANT ALL PRIVILEGES ON DATABASE knowledge_graph TO cspro_admin;

# Enable pgvector extension
\c knowledge_graph
CREATE EXTENSION vector;
\q
```

### 2. Environment Setup

```bash
# Clone the repository
cd knowledge-bot

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Configure environment variables
cp .env.example .env
# Edit .env file with your configurations
```

### 3. Environment Variables

Update the `.env` file with your configurations:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5433
DB_NAME=knowledge_graph
DB_USER=cspro_admin
DB_PASSWORD=super-secret-123

# OpenAI Configuration (Required)
OPENAI_API_KEY=your_openai_api_key_here

# JWT Configuration (must match main NestJS application)
JWT_SECRET="asdvgfcvblcvbrlqwedkamklmbrwewoved"
ALGORITHM="HS256"

# Main NestJS Application URL for user verification
MAIN_APP_URL="http://localhost:5000"

# Legacy Authentication (for backward compatibility)
SECRET_KEY="your_secret_key_here"
REFRESH_SECRET_KEY="your_refresh_secret_key_here"

# API Documentation
DOCS_USERNAME=admin
DOCS_PASSWORD=password
```

### 4. Database Migration

```bash
# Initialize Alembic (if not already done)
alembic init alembic

# Create initial migration
alembic revision --autogenerate -m "Initial migration"

# Apply migrations
alembic upgrade head
```

### 5. Run the Application

```bash
# Development mode
python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 5000

# Production mode
python -m uvicorn src.main:app --host 0.0.0.0 --port 5000
```

## API Endpoints

### FAQ Management
- `POST /api/v1/faq/` - Create FAQ
- `GET /api/v1/faq/` - List FAQs with pagination
- `GET /api/v1/faq/{faq_id}` - Get specific FAQ
- `PUT /api/v1/faq/{faq_id}` - Update FAQ
- `DELETE /api/v1/faq/{faq_id}` - Delete FAQ
- `GET /api/v1/faq/categories` - Get all categories
- `GET /api/v1/faq/popular` - Get popular FAQs
- `POST /api/v1/faq/bulk` - Bulk create FAQs

### RAG System & Chat
- `POST /api/v1/chat/` - Chat with RAG system (supports anonymous users)
- `POST /api/v1/chat/query` - Advanced RAG query (authenticated only)
- `GET /api/v1/chat/similar/{faq_id}` - Get similar FAQs
- `POST /api/v1/chat/embeddings/generate` - Generate embeddings
- `GET /api/v1/chat/health` - RAG system health check
- `GET /api/v1/chat/history` - Get user chat history (authenticated)
- `POST /api/v1/chat/sessions` - Create new chat session (authenticated)
- `GET /api/v1/chat/sessions/{id}/messages` - Get session messages (authenticated)
- `GET /api/v1/chat/anonymous-history/{id}` - Get anonymous session history

### Authentication & User Management
- `GET /api/v1/auth/user-context` - Show user context from headers
- `GET /api/v1/auth/validate-user` - Validate user with main app
- `POST /api/v1/auth/validate-token` - Validate JWT token with main app
- `GET /api/v1/auth/user/{id}/profile` - Get user profile from main app
- `POST /api/v1/auth/user/{id}/activity` - Log user activity to main app
- `GET /api/v1/auth/user/{id}/permissions/{permission}` - Check user permission
- `GET /api/v1/auth/user/{id}/roles/{role}` - Check user role
- `POST /api/v1/auth/anonymous-session` - Create anonymous session
- `GET /api/v1/auth/anonymous-session/{id}` - Get anonymous session info
- `DELETE /api/v1/auth/anonymous-sessions/cleanup` - Clean up expired sessions

**Features:**
- **API Integration**: Makes comprehensive API calls to main NestJS application
- **User Validation**: Validates users by calling main app endpoints
- **Permission Checking**: Real-time permission and role verification
- **Activity Logging**: Logs all user activities to main application
- **Anonymous User Support**: Limited functionality for unauthenticated users

📖 **See [AUTHENTICATION.md](./AUTHENTICATION.md) for authentication documentation.**
📖 **See [API_INTEGRATION.md](./API_INTEGRATION.md) for API integration details.**

### System
- `GET /health` - System health check
- `GET /docs` - API documentation (protected)

## Usage Examples

### Create FAQ
```bash
curl -X POST "http://localhost:5000/api/v1/faq/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "question": "How do I reset my password?",
    "answer": "To reset your password, click on the Forgot Password link on the login page and follow the instructions.",
    "category": "account",
    "tags": ["password", "reset", "account"],
    "priority": 5
  }'
```

### Chat with RAG System
```bash
curl -X POST "http://localhost:5000/api/v1/chat/" \
  -H "Content-Type: application/json" \
  -d '"How do I reset my password?"'
```

### Generate Embeddings
```bash
curl -X POST "http://localhost:5000/api/v1/chat/embeddings/generate" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Verify JWT Token
```bash
curl -X GET "http://localhost:5000/api/v1/auth/verify" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Verify Token with Main App
```bash
curl -X GET "http://localhost:5000/api/v1/auth/verify-with-main-app" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Development

### Running Tests
```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run tests
pytest
```

### Code Quality
```bash
# Install development dependencies
pip install black flake8 mypy

# Format code
black src/

# Lint code
flake8 src/

# Type checking
mypy src/
```

## Deployment

### Docker
```bash
# Build image
docker build -t knowledge-bot .

# Run container
docker run -p 5000:5000 --env-file .env knowledge-bot
```

### Docker Compose
```bash
# Start all services
docker-compose up -d
```

## Monitoring

- **Health Checks**: `/health` and `/api/v1/chat/health`
- **Metrics**: Built-in FastAPI metrics
- **Logging**: Structured logging with Python logging
- **Error Tracking**: Slack integration for error alerts

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
