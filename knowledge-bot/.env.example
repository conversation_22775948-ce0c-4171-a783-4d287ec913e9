DB_HOST=localhost
DB_PORT=5433
DB_NAME=cspro
DB_USER=cspro_admin
DB_PASSWORD=super-secret-123
DATABASE_URL="postgresql://cspro_admin:super-secret-123@localhost:5433/cspro?schema=public"
ENVIRONMENT=DEV
DOCS_USERNAME=admin
DOCS_PASSWORD=password

# Main NestJS Application Configuration
MAIN_APP_URL="http://localhost:3000"
MAIN_APP_API_KEY=""  # API key for calling main app (if required)
REQUEST_TIMEOUT=10   # Timeout for API calls to main app (seconds)

# Optional API Key for basic protection (leave empty to disable)
KNOWLEDGE_BOT_API_KEY=""

# OpenAI Configuration (Required for RAG system)
OPENAI_API_KEY="your_openai_api_key_here"

# No longer needed - authentication handled by main NestJS app

# Slack Integration (Optional)
SLACK_WEBHOOK_URL="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"

# Logging Level
LOG_LEVEL="INFO"
