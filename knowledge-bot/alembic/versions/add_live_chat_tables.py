"""Add live chat tables

Revision ID: add_live_chat_tables
Revises: 
Create Date: 2025-01-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_live_chat_tables'
down_revision = None  # Update this with the latest revision
branch_labels = None
depends_on = None


def upgrade():
    # Create live_chat_sessions table
    op.create_table('live_chat_sessions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('agent_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False, default='waiting'),
        sa.Column('subject', sa.String(length=255), nullable=True),
        sa.Column('priority', sa.Integer(), nullable=False, default=1),
        sa.Column('anonymous_user_id', sa.String(length=255), nullable=True),
        sa.Column('anonymous_user_name', sa.String(length=255), nullable=True),
        sa.Column('user_email', sa.String(length=255), nullable=True),
        sa.Column('user_phone', sa.String(length=50), nullable=True),
        sa.Column('started_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('assigned_at', sa.DateTime(), nullable=True),
        sa.Column('closed_at', sa.DateTime(), nullable=True),
        sa.Column('last_activity_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('chatbot_session_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('user_rating', sa.Integer(), nullable=True),
        sa.Column('user_feedback', sa.Text(), nullable=True),
        sa.Column('agent_notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for live_chat_sessions
    op.create_index('idx_live_chat_status', 'live_chat_sessions', ['status'])
    op.create_index('idx_live_chat_agent', 'live_chat_sessions', ['agent_id'])
    op.create_index('idx_live_chat_user', 'live_chat_sessions', ['user_id'])
    op.create_index('idx_live_chat_activity', 'live_chat_sessions', ['last_activity_at'])

    # Create live_chat_messages table
    op.create_table('live_chat_messages',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('session_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('message_type', sa.String(length=20), nullable=False, default='user'),
        sa.Column('sender_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('sender_name', sa.String(length=255), nullable=True),
        sa.Column('sender_email', sa.String(length=255), nullable=True),
        sa.Column('is_read', sa.Boolean(), nullable=False, default=False),
        sa.Column('read_at', sa.DateTime(), nullable=True),
        sa.Column('attachments', sa.Text(), nullable=True),
        sa.Column('is_from_bot', sa.Boolean(), nullable=False, default=False),
        sa.Column('bot_response_data', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['session_id'], ['live_chat_sessions.id'], ondelete='CASCADE')
    )
    
    # Create indexes for live_chat_messages
    op.create_index('idx_live_chat_msg_session', 'live_chat_messages', ['session_id'])
    op.create_index('idx_live_chat_msg_type', 'live_chat_messages', ['message_type'])
    op.create_index('idx_live_chat_msg_created', 'live_chat_messages', ['created_at'])


def downgrade():
    # Drop indexes first
    op.drop_index('idx_live_chat_msg_created', table_name='live_chat_messages')
    op.drop_index('idx_live_chat_msg_type', table_name='live_chat_messages')
    op.drop_index('idx_live_chat_msg_session', table_name='live_chat_messages')
    op.drop_index('idx_live_chat_activity', table_name='live_chat_sessions')
    op.drop_index('idx_live_chat_user', table_name='live_chat_sessions')
    op.drop_index('idx_live_chat_agent', table_name='live_chat_sessions')
    op.drop_index('idx_live_chat_status', table_name='live_chat_sessions')
    
    # Drop tables
    op.drop_table('live_chat_messages')
    op.drop_table('live_chat_sessions')
