#!/usr/bin/env python3
"""
Test script for Knowledge Bot authentication with main NestJS app.
This script demonstrates how to:
1. Get a JWT token from the main NestJS app
2. Use that token to access Knowledge Bot endpoints
3. Verify user information extraction
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

load_dotenv()

# Configuration
MAIN_APP_URL = os.getenv("MAIN_APP_URL", "http://localhost:5000")
KNOWLEDGE_BOT_URL = os.getenv("KNOWLEDGE_BOT_URL", "http://localhost:5001")

async def login_to_main_app(email: str, password: str) -> str:
    """Login to main NestJS app and get JWT token"""
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{MAIN_APP_URL}/auth/login",
                json={"email": email, "password": password}
            )
            if response.status_code == 200:
                data = response.json()
                return data.get("token")
            else:
                print(f"Login failed: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"Error logging in: {e}")
            return None

async def test_knowledge_bot_auth(token: str):
    """Test Knowledge Bot authentication endpoints"""
    headers = {"Authorization": f"Bearer {token}"}
    
    async with httpx.AsyncClient() as client:
        print("\n=== Testing Knowledge Bot Authentication ===")
        
        # Test basic token verification
        print("\n1. Testing /api/v1/auth/verify")
        try:
            response = await client.get(
                f"{KNOWLEDGE_BOT_URL}/api/v1/auth/verify",
                headers=headers
            )
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                user_info = response.json()
                print(f"User Info: {json.dumps(user_info, indent=2)}")
            else:
                print(f"Error: {response.text}")
        except Exception as e:
            print(f"Error: {e}")
        
        # Test verification with main app
        print("\n2. Testing /api/v1/auth/verify-with-main-app")
        try:
            response = await client.get(
                f"{KNOWLEDGE_BOT_URL}/api/v1/auth/verify-with-main-app",
                headers=headers
            )
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                verification_result = response.json()
                print(f"Verification Result: {json.dumps(verification_result, indent=2)}")
            else:
                print(f"Error: {response.text}")
        except Exception as e:
            print(f"Error: {e}")
        
        # Test FAQ creation with user info
        print("\n3. Testing FAQ creation with user authentication")
        try:
            faq_data = {
                "question": "Test question from authenticated user",
                "answer": "This FAQ was created by an authenticated user",
                "category": "test",
                "tags": ["test", "auth"],
                "priority": 1
            }
            response = await client.post(
                f"{KNOWLEDGE_BOT_URL}/api/v1/faq/",
                headers=headers,
                json=faq_data
            )
            print(f"Status: {response.status_code}")
            if response.status_code == 201:
                faq = response.json()
                print(f"Created FAQ: {json.dumps(faq, indent=2)}")
                print(f"Created by user ID: {faq.get('created_by')}")
            else:
                print(f"Error: {response.text}")
        except Exception as e:
            print(f"Error: {e}")

async def main():
    """Main test function"""
    print("Knowledge Bot Authentication Test")
    print("=" * 50)
    
    # Get credentials from user or environment
    email = input("Enter email for main app login: ") or "<EMAIL>"
    password = input("Enter password: ") or "password123"
    
    # Login to main app
    print(f"\nLogging in to main app at {MAIN_APP_URL}...")
    token = await login_to_main_app(email, password)
    
    if not token:
        print("Failed to get token from main app. Please check your credentials and main app status.")
        return
    
    print(f"✅ Successfully obtained JWT token")
    print(f"Token (first 50 chars): {token[:50]}...")
    
    # Test Knowledge Bot authentication
    await test_knowledge_bot_auth(token)
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    asyncio.run(main())
