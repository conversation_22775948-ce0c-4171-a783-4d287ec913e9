#!/usr/bin/env python3
"""
Test script for Knowledge Bot simplified authentication system.
This script tests the header-based authentication approach where
all authentication is handled by the main NestJS application.
"""

import requests
import json
import sys
from datetime import datetime

BASE_URL = "http://localhost:5000"

def test_user_context_headers():
    """Test user context extraction from headers."""
    print("🔍 Testing User Context from Headers...")
    
    # Test with user context headers (simulating main app request)
    headers = {
        "X-User-ID": "user-123-456",
        "X-User-Email": "<EMAIL>",
        "X-User-Roles": "USER,ADMIN"
    }
    
    response = requests.get(f"{BASE_URL}/api/v1/auth/user-context", headers=headers)
    if response.status_code == 200:
        data = response.json()
        print("✅ User context extracted successfully")
        print(f"   User ID: {data['user_context']['user_id']}")
        print(f"   Email: {data['user_context']['user_email']}")
        print(f"   Authenticated: {data['user_context']['is_authenticated']}")
    else:
        print("❌ Failed to extract user context")

def test_anonymous_session():
    """Test anonymous session creation and usage."""
    print("\n🔍 Testing Anonymous Session...")
    
    # Create anonymous session
    response = requests.post(f"{BASE_URL}/api/v1/auth/anonymous-session")
    if response.status_code == 200:
        session_data = response.json()
        session_id = session_data["session_id"]
        print(f"✅ Anonymous session created: {session_id}")
        
        # Test chat with anonymous session
        headers = {"X-Anonymous-Session-ID": session_id}
        chat_response = requests.post(
            f"{BASE_URL}/api/v1/chat/",
            headers=headers,
            json="How do I reset my password?"
        )
        
        if chat_response.status_code == 200:
            print("✅ Anonymous chat successful")
            
            # Get session history
            history_response = requests.get(
                f"{BASE_URL}/api/v1/chat/anonymous-history/{session_id}"
            )
            if history_response.status_code == 200:
                print("✅ Anonymous session history retrieved")
            else:
                print("❌ Failed to get anonymous session history")
        else:
            print("❌ Anonymous chat failed")
    else:
        print("❌ Failed to create anonymous session")

def test_chat_without_auth():
    """Test chat without any authentication."""
    print("\n🔍 Testing Chat Without Authentication...")
    
    response = requests.post(
        f"{BASE_URL}/api/v1/chat/",
        json="What is the weather like?"
    )
    
    if response.status_code == 200:
        print("✅ Anonymous chat (no session) successful")
    else:
        print("❌ Anonymous chat (no session) failed")

def test_protected_endpoints_without_headers():
    """Test protected endpoints without user context headers."""
    print("\n🔍 Testing Protected Endpoints Without Headers...")
    
    # Test FAQ creation (should fail without user context)
    response = requests.post(
        f"{BASE_URL}/api/v1/faq/",
        json={
            "question": "Test question",
            "answer": "Test answer"
        }
    )
    
    if response.status_code == 401:
        print("✅ FAQ creation properly protected")
    else:
        print("❌ FAQ creation not properly protected")
    
    # Test advanced RAG query (should fail without user context)
    response = requests.post(
        f"{BASE_URL}/api/v1/chat/query",
        json={
            "query": "Test query",
            "max_results": 5
        }
    )
    
    if response.status_code == 401:
        print("✅ Advanced RAG query properly protected")
    else:
        print("❌ Advanced RAG query not properly protected")

def test_protected_endpoints_with_headers():
    """Test protected endpoints with user context headers."""
    print("\n🔍 Testing Protected Endpoints With Headers...")
    
    # Headers simulating request from main NestJS app
    headers = {
        "X-User-ID": "user-123-456",
        "X-User-Email": "<EMAIL>",
        "X-User-Roles": "USER,ADMIN"
    }
    
    # Test FAQ creation with user context
    response = requests.post(
        f"{BASE_URL}/api/v1/faq/",
        headers=headers,
        json={
            "question": "Test authenticated question",
            "answer": "Test authenticated answer",
            "category": "test"
        }
    )
    
    if response.status_code == 201:
        print("✅ Authenticated FAQ creation successful")
    else:
        print(f"❌ Authenticated FAQ creation failed: {response.status_code}")
        
    # Test advanced RAG query with user context
    response = requests.post(
        f"{BASE_URL}/api/v1/chat/query",
        headers=headers,
        json={
            "query": "Test authenticated query",
            "max_results": 5
        }
    )
    
    if response.status_code == 200:
        print("✅ Authenticated RAG query successful")
    else:
        print(f"❌ Authenticated RAG query failed: {response.status_code}")

def test_api_key_protection():
    """Test optional API key protection."""
    print("\n🔍 Testing Optional API Key Protection...")
    
    # Test cleanup endpoint without API key
    response = requests.delete(f"{BASE_URL}/api/v1/auth/anonymous-sessions/cleanup")
    
    if response.status_code in [200, 401]:
        if response.status_code == 200:
            print("✅ Cleanup successful (API key not configured)")
        else:
            print("✅ Cleanup properly protected by API key")
    else:
        print("❌ Unexpected response from cleanup endpoint")

def test_public_endpoints():
    """Test public endpoints that should work without authentication."""
    print("\n🔍 Testing Public Endpoints...")
    
    # Test FAQ listing
    response = requests.get(f"{BASE_URL}/api/v1/faq/")
    if response.status_code == 200:
        print("✅ FAQ listing accessible")
    else:
        print("❌ FAQ listing not accessible")
    
    # Test FAQ categories
    response = requests.get(f"{BASE_URL}/api/v1/faq/categories")
    if response.status_code == 200:
        print("✅ FAQ categories accessible")
    else:
        print("❌ FAQ categories not accessible")
    
    # Test health check
    response = requests.get(f"{BASE_URL}/health")
    if response.status_code == 200:
        print("✅ Health check accessible")
    else:
        print("❌ Health check not accessible")

def main():
    """Run all authentication tests."""
    print("🚀 Knowledge Bot Simplified Authentication Tests")
    print("=" * 55)
    print("Testing header-based authentication approach")
    print("All authentication is handled by the main NestJS app")
    print("=" * 55)
    
    try:
        # Test if the server is running
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not responding properly")
            sys.exit(1)
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to the server. Make sure it's running on http://localhost:5000")
        sys.exit(1)
    
    print("✅ Server is running")
    
    # Run tests
    test_public_endpoints()
    test_user_context_headers()
    test_anonymous_session()
    test_chat_without_auth()
    test_protected_endpoints_without_headers()
    test_protected_endpoints_with_headers()
    test_api_key_protection()
    
    print("\n" + "=" * 55)
    print("🏁 Simplified authentication tests completed!")
    print("\nKey Points:")
    print("- ✅ No JWT validation in knowledge bot")
    print("- ✅ User context passed via headers")
    print("- ✅ Anonymous users supported")
    print("- ✅ Protected endpoints require user context headers")
    print("- ✅ Public endpoints work without authentication")
    print("\nFor integration details, see AUTHENTICATION.md")

if __name__ == "__main__":
    main()
