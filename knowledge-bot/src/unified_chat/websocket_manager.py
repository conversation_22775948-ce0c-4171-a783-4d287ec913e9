import json
import logging
from typing import Dict, List, Optional, Set
from uuid import UUI<PERSON>
from fastapi import WebSocket
from datetime import datetime

from src.live_chat.websocket_manager import LiveChatWebSocketManager

logger = logging.getLogger(__name__)


class UnifiedChatWebSocketManager:
    """
    Unified WebSocket manager that handles both AI chat and live chat in the same connection.
    Extends the live chat manager to support AI interactions.
    """
    
    def __init__(self):
        # Inherit live chat functionality
        self.live_chat_manager = LiveChatWebSocketManager()
        
        # Additional tracking for unified sessions
        self.unified_sessions: Dict[str, Dict] = {}  # unified_session_id -> session_data
        self.session_websockets: Dict[str, Set[WebSocket]] = {}  # unified_session_id -> websockets
        
        # AI chat specific connections
        self.ai_chat_connections: Dict[str, WebSocket] = {}  # user_id/anonymous_id -> websocket

    async def connect_unified_chat(
        self,
        websocket: WebSocket,
        user_id: Optional[str] = None,
        anonymous_id: Optional[str] = None,
        unified_session_id: Optional[str] = None
    ):
        """Connect a user to unified chat (AI + Live Chat)."""
        await websocket.accept()
        
        # Store connection metadata
        metadata = {
            "type": "unified_chat",
            "user_id": user_id,
            "anonymous_id": anonymous_id,
            "unified_session_id": unified_session_id,
            "connected_at": datetime.utcnow(),
            "is_authenticated": user_id is not None,
            "chat_mode": "ai"  # ai, live, transitioning
        }
        
        # Store connection
        connection_key = user_id or anonymous_id
        if connection_key:
            self.ai_chat_connections[connection_key] = websocket
        
        # Add to unified session room if provided
        if unified_session_id:
            await self.join_unified_session(websocket, unified_session_id)
        
        # Store metadata in live chat manager for compatibility
        self.live_chat_manager.connection_metadata[websocket] = metadata
        
        logger.info(f"User connected to unified chat: user_id={user_id}, anonymous_id={anonymous_id}")
        
        # Send connection confirmation
        await self.send_to_connection(websocket, {
            "type": "unified_chat_connected",
            "data": {
                "message": "Connected to unified chat",
                "user_id": user_id,
                "anonymous_id": anonymous_id,
                "unified_session_id": unified_session_id,
                "chat_mode": "ai",
                "timestamp": datetime.utcnow().isoformat()
            }
        })

    async def disconnect_unified_chat(self, websocket: WebSocket):
        """Disconnect from unified chat."""
        if websocket not in self.live_chat_manager.connection_metadata:
            return
        
        metadata = self.live_chat_manager.connection_metadata[websocket]
        
        # Remove from AI chat connections
        connection_key = metadata.get("user_id") or metadata.get("anonymous_id")
        if connection_key and connection_key in self.ai_chat_connections:
            del self.ai_chat_connections[connection_key]
        
        # Remove from unified session rooms
        unified_session_id = metadata.get("unified_session_id")
        if unified_session_id:
            await self.leave_unified_session(websocket, unified_session_id)
        
        # Use live chat manager for cleanup
        await self.live_chat_manager.disconnect(websocket)
        
        logger.info(f"User disconnected from unified chat")

    async def join_unified_session(self, websocket: WebSocket, unified_session_id: str):
        """Add a WebSocket to a unified session room."""
        if unified_session_id not in self.session_websockets:
            self.session_websockets[unified_session_id] = set()
        
        self.session_websockets[unified_session_id].add(websocket)
        
        # Update metadata
        if websocket in self.live_chat_manager.connection_metadata:
            self.live_chat_manager.connection_metadata[websocket]["unified_session_id"] = unified_session_id
        
        logger.debug(f"WebSocket joined unified session {unified_session_id}")

    async def leave_unified_session(self, websocket: WebSocket, unified_session_id: str):
        """Remove a WebSocket from a unified session room."""
        if unified_session_id in self.session_websockets:
            self.session_websockets[unified_session_id].discard(websocket)
            
            # Clean up empty rooms
            if not self.session_websockets[unified_session_id]:
                del self.session_websockets[unified_session_id]
        
        logger.debug(f"WebSocket left unified session {unified_session_id}")

    async def transition_to_live_chat(
        self,
        websocket: WebSocket,
        live_chat_session_id: str
    ):
        """Transition a unified chat session to live chat mode."""
        if websocket not in self.live_chat_manager.connection_metadata:
            return
        
        metadata = self.live_chat_manager.connection_metadata[websocket]
        
        # Update chat mode
        metadata["chat_mode"] = "live"
        metadata["live_chat_session_id"] = live_chat_session_id
        
        # Add to live chat session room
        await self.live_chat_manager.join_session_room(websocket, live_chat_session_id)
        
        # Notify about transition
        await self.send_to_connection(websocket, {
            "type": "transitioned_to_live_chat",
            "data": {
                "message": "You are now connected to a live agent",
                "live_chat_session_id": live_chat_session_id,
                "chat_mode": "live",
                "timestamp": datetime.utcnow().isoformat()
            }
        })
        
        logger.info(f"Transitioned session to live chat: {live_chat_session_id}")

    async def send_ai_response(
        self,
        unified_session_id: str,
        ai_response: dict
    ):
        """Send AI response to all connections in a unified session."""
        if unified_session_id not in self.session_websockets:
            logger.warning(f"No connections found for unified session {unified_session_id}")
            return
        
        message = {
            "type": "ai_response",
            "data": ai_response
        }
        
        await self.broadcast_to_unified_session(unified_session_id, message)

    async def send_escalation_offer(
        self,
        unified_session_id: str,
        escalation_data: dict
    ):
        """Send escalation offer to user."""
        message = {
            "type": "escalation_offer",
            "data": escalation_data
        }
        
        await self.broadcast_to_unified_session(unified_session_id, message)

    async def send_live_chat_message(
        self,
        live_chat_session_id: str,
        message_data: dict
    ):
        """Send live chat message (delegates to live chat manager)."""
        await self.live_chat_manager.send_to_session(live_chat_session_id, {
            "type": "live_chat_message",
            "data": message_data
        })

    async def broadcast_to_unified_session(
        self,
        unified_session_id: str,
        message: dict
    ):
        """Broadcast a message to all connections in a unified session."""
        if unified_session_id not in self.session_websockets:
            logger.warning(f"No connections found for unified session {unified_session_id}")
            return
        
        disconnected_connections = []
        
        for websocket in self.session_websockets[unified_session_id]:
            try:
                await self.send_to_connection(websocket, message)
            except Exception as e:
                logger.error(f"Error sending message to unified session {unified_session_id}: {e}")
                disconnected_connections.append(websocket)
        
        # Clean up disconnected connections
        for websocket in disconnected_connections:
            await self.disconnect_unified_chat(websocket)

    async def send_to_connection(self, websocket: WebSocket, message: dict):
        """Send a message to a specific WebSocket connection."""
        try:
            message_str = json.dumps(message, default=str)
            await websocket.send_text(message_str)
        except Exception as e:
            logger.error(f"Error sending WebSocket message: {e}")
            raise

    async def send_to_user(self, user_id: str, message: dict):
        """Send a message to a specific user."""
        if user_id in self.ai_chat_connections:
            try:
                await self.send_to_connection(self.ai_chat_connections[user_id], message)
            except Exception as e:
                logger.error(f"Error sending message to user {user_id}: {e}")
                await self.disconnect_unified_chat(self.ai_chat_connections[user_id])

    async def send_to_anonymous_user(self, anonymous_id: str, message: dict):
        """Send a message to a specific anonymous user."""
        if anonymous_id in self.ai_chat_connections:
            try:
                await self.send_to_connection(self.ai_chat_connections[anonymous_id], message)
            except Exception as e:
                logger.error(f"Error sending message to anonymous user {anonymous_id}: {e}")
                await self.disconnect_unified_chat(self.ai_chat_connections[anonymous_id])

    def get_connection_stats(self) -> dict:
        """Get connection statistics."""
        live_chat_stats = self.live_chat_manager.get_connection_stats()
        
        return {
            **live_chat_stats,
            "unified_chat_connections": len(self.ai_chat_connections),
            "unified_sessions": len(self.session_websockets),
            "ai_chat_connections": len([
                conn for conn in self.live_chat_manager.connection_metadata.values()
                if conn.get("chat_mode") == "ai"
            ]),
            "live_chat_connections": len([
                conn for conn in self.live_chat_manager.connection_metadata.values()
                if conn.get("chat_mode") == "live"
            ]),
            "timestamp": datetime.utcnow().isoformat()
        }

    def is_user_connected(self, user_id: str) -> bool:
        """Check if a user is connected to unified chat."""
        return user_id in self.ai_chat_connections

    def is_anonymous_user_connected(self, anonymous_id: str) -> bool:
        """Check if an anonymous user is connected to unified chat."""
        return anonymous_id in self.ai_chat_connections

    def get_user_chat_mode(self, user_id: str) -> Optional[str]:
        """Get the current chat mode for a user."""
        if user_id in self.ai_chat_connections:
            websocket = self.ai_chat_connections[user_id]
            if websocket in self.live_chat_manager.connection_metadata:
                return self.live_chat_manager.connection_metadata[websocket].get("chat_mode")
        return None

    async def notify_agent_assignment(
        self,
        unified_session_id: str,
        live_chat_session_id: str,
        agent_id: str
    ):
        """Notify about agent assignment."""
        message = {
            "type": "agent_assigned",
            "data": {
                "message": "A support agent has joined the chat",
                "live_chat_session_id": live_chat_session_id,
                "agent_id": agent_id,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        
        await self.broadcast_to_unified_session(unified_session_id, message)

    async def notify_session_closed(
        self,
        unified_session_id: str,
        live_chat_session_id: str
    ):
        """Notify about session closure."""
        message = {
            "type": "session_closed",
            "data": {
                "message": "Chat session has been closed",
                "live_chat_session_id": live_chat_session_id,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        
        await self.broadcast_to_unified_session(unified_session_id, message)

    # Delegate agent-related methods to live chat manager
    async def connect_agent(self, websocket: WebSocket, agent_id: str):
        """Connect an agent (delegates to live chat manager)."""
        return await self.live_chat_manager.connect_agent(websocket, agent_id)

    async def send_to_agent(self, agent_id: str, message: dict):
        """Send message to agent (delegates to live chat manager)."""
        return await self.live_chat_manager.send_to_agent(agent_id, message)

    async def broadcast_to_agents(self, message: dict):
        """Broadcast to agents (delegates to live chat manager)."""
        return await self.live_chat_manager.broadcast_to_agents(message)

    def get_agent_connection_count(self) -> int:
        """Get agent connection count (delegates to live chat manager)."""
        return self.live_chat_manager.get_agent_connection_count()

    def is_agent_connected(self, agent_id: str) -> bool:
        """Check if agent is connected (delegates to live chat manager)."""
        return self.live_chat_manager.is_agent_connected(agent_id)
