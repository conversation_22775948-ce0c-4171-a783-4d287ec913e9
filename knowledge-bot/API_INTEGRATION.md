# API Integration with Main NestJS Application

## Overview

The Knowledge Bot microservice makes comprehensive API calls to the main NestJS application for user data retrieval, validation, and activity logging. This document outlines all the API endpoints that the knowledge bot expects from your main application.

## Required API Endpoints in Main NestJS Application

### User Management Endpoints

#### 1. Get User by ID
```
GET /api/users/{userId}
```
**Purpose**: Retrieve complete user information by user ID.

**Response**:
```json
{
  "id": "user-uuid",
  "email": "<EMAIL>",
  "fullName": "<PERSON>",
  "isActive": true,
  "roles": ["USER", "ADMIN"],
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

#### 2. Get User by Email
```
GET /api/users/by-email/{email}
```
**Purpose**: Retrieve user information by email address.

**Response**: Same as Get User by ID

#### 3. Get User Profile
```
GET /api/users/{userId}/profile
```
**Purpose**: Get complete user profile with additional details.

**Response**:
```json
{
  "id": "user-uuid",
  "email": "<EMAIL>",
  "fullName": "John Doe",
  "avatar": "https://example.com/avatar.jpg",
  "bio": "User biography",
  "preferences": {
    "theme": "dark",
    "language": "en"
  },
  "lastActivity": "2023-01-01T00:00:00Z"
}
```

#### 4. Update User Last Activity
```
PATCH /api/users/{userId}/last-activity
```
**Purpose**: Update user's last activity timestamp.

**Request Body**:
```json
{
  "lastActivity": "2023-01-01T00:00:00Z",
  "source": "knowledge-bot"
}
```

### Authentication Endpoints

#### 5. Validate Token
```
GET /api/auth/validate
Headers: Authorization: Bearer {token}
```
**Purpose**: Validate JWT token and return user information.

**Response**:
```json
{
  "id": "user-uuid",
  "email": "<EMAIL>",
  "roles": ["USER"],
  "permissions": ["read", "write"],
  "isActive": true,
  "tokenValid": true
}
```

### Permission & Role Management

#### 6. Get User Permissions
```
GET /api/users/{userId}/permissions
```
**Purpose**: Get all permissions for a user.

**Response**:
```json
{
  "userId": "user-uuid",
  "permissions": [
    "faq:read",
    "faq:create",
    "faq:edit",
    "faq:delete",
    "chat:access"
  ]
}
```

#### 7. Get User Roles
```
GET /api/users/{userId}/roles
```
**Purpose**: Get all roles for a user.

**Response**:
```json
{
  "userId": "user-uuid",
  "roles": ["USER", "FAQ_EDITOR", "ADMIN"]
}
```

#### 8. Check Specific Permission
```
GET /api/users/{userId}/permissions/{permission}
```
**Purpose**: Check if user has a specific permission.

**Response**:
```json
{
  "userId": "user-uuid",
  "permission": "faq:edit",
  "hasPermission": true
}
```

#### 9. Check Specific Role
```
GET /api/users/{userId}/roles/{role}
```
**Purpose**: Check if user has a specific role.

**Response**:
```json
{
  "userId": "user-uuid",
  "role": "ADMIN",
  "hasRole": false
}
```

### User Preferences

#### 10. Get User Preferences
```
GET /api/users/{userId}/preferences
```
**Purpose**: Get user preferences and settings.

**Response**:
```json
{
  "userId": "user-uuid",
  "preferences": {
    "theme": "dark",
    "language": "en",
    "notifications": {
      "email": true,
      "push": false
    },
    "knowledgeBot": {
      "maxResults": 10,
      "autoSave": true
    }
  }
}
```

### Activity Logging

#### 11. Log User Activity
```
POST /api/users/activity
```
**Purpose**: Log user activity for analytics and auditing.

**Request Body**:
```json
{
  "userId": "user-uuid",
  "activity": "faq_created",
  "details": {
    "question": "How to reset password?",
    "category": "authentication",
    "priority": 5
  },
  "timestamp": "2023-01-01T00:00:00Z",
  "source": "knowledge-bot"
}
```

**Response**:
```json
{
  "id": "activity-uuid",
  "userId": "user-uuid",
  "activity": "faq_created",
  "timestamp": "2023-01-01T00:00:00Z",
  "logged": true
}
```

### Notifications

#### 12. Send Notification
```
POST /api/notifications
```
**Purpose**: Send notification to user.

**Request Body**:
```json
{
  "userId": "user-uuid",
  "notification": {
    "title": "FAQ Created",
    "message": "Your FAQ has been successfully created",
    "type": "success",
    "data": {
      "faqId": "faq-uuid"
    }
  },
  "source": "knowledge-bot",
  "timestamp": "2023-01-01T00:00:00Z"
}
```

## Authentication for API Calls

### Option 1: API Key Authentication
Add API key to all requests from knowledge bot:

```
Headers:
X-API-Key: your-api-key-here
```

### Option 2: Service Token
Use a service-specific JWT token:

```
Headers:
Authorization: Bearer service-token-here
```

### Option 3: IP Whitelist
Whitelist the knowledge bot's IP address in your main application.

## Error Handling

All endpoints should return consistent error responses:

```json
{
  "error": {
    "code": "USER_NOT_FOUND",
    "message": "User with ID user-uuid not found",
    "timestamp": "2023-01-01T00:00:00Z"
  }
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created (for POST requests)
- `204` - No Content (for successful updates)
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

## Configuration in Knowledge Bot

Update your `.env` file:

```bash
# Main NestJS Application Configuration
MAIN_APP_URL="http://localhost:3000"
MAIN_APP_API_KEY="your-api-key-here"
REQUEST_TIMEOUT=10

# Optional API Key for protecting knowledge bot
KNOWLEDGE_BOT_API_KEY="knowledge-bot-api-key"
```

## Usage Examples

### In Knowledge Bot Service

```python
# Get user data
user_data = await auth_service.get_user_by_id("user-123")

# Check permission
has_permission = await auth_service.check_user_permission("user-123", "faq:edit")

# Log activity
await auth_service.log_user_activity("user-123", "faq_created", {
    "question": "How to reset password?",
    "category": "auth"
})

# Send notification
await auth_service.notify_user("user-123", {
    "title": "FAQ Created",
    "message": "Your FAQ was created successfully"
})
```

### In Main NestJS Application

```typescript
// Example user controller
@Controller('api/users')
export class UsersController {
  @Get(':id')
  async getUser(@Param('id') id: string) {
    return await this.usersService.findById(id);
  }

  @Get(':id/permissions')
  async getUserPermissions(@Param('id') id: string) {
    const permissions = await this.permissionsService.getUserPermissions(id);
    return { userId: id, permissions };
  }

  @Post('activity')
  async logActivity(@Body() activityData: LogActivityDto) {
    return await this.activityService.log(activityData);
  }
}
```

## Testing API Integration

Use the knowledge bot's test endpoints to verify integration:

```bash
# Test user validation
curl -X GET "http://localhost:5000/api/v1/auth/validate-user" \
  -H "X-User-ID: user-123" \
  -H "X-User-Email: <EMAIL>"

# Test permission check
curl -X GET "http://localhost:5000/api/v1/auth/user/user-123/permissions/faq:edit"

# Test activity logging
curl -X POST "http://localhost:5000/api/v1/auth/user/user-123/activity" \
  -H "Content-Type: application/json" \
  -d '{"activity": "test_activity", "details": {"test": true}}'
```

## Security Considerations

1. **API Key Security**: Store API keys securely and rotate them regularly
2. **Rate Limiting**: Implement rate limiting on your main app's API endpoints
3. **Input Validation**: Validate all input data in your main application
4. **Logging**: Log all API calls for monitoring and debugging
5. **HTTPS**: Use HTTPS for all API communications in production

## Monitoring and Debugging

1. **Health Checks**: Implement health check endpoints
2. **Metrics**: Track API call success/failure rates
3. **Logging**: Log all API interactions with timestamps
4. **Alerts**: Set up alerts for API failures or high error rates
