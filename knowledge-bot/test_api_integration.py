#!/usr/bin/env python3
"""
Test script for Knowledge Bot API integration with main NestJS application.
This script tests all the API calls that the knowledge bot makes to the main app.
"""

import requests
import json
import sys
from datetime import datetime
from typing import Dict, Any

KNOWLEDGE_BOT_URL = "http://localhost:5000"
MAIN_APP_URL = "http://localhost:3000"

def test_user_validation_endpoint():
    """Test the user validation endpoint that calls main app."""
    print("🔍 Testing User Validation with Main App...")
    
    # Test with user context headers
    headers = {
        "X-User-ID": "test-user-123",
        "X-User-Email": "<EMAIL>",
        "X-User-Roles": "USER,ADMIN"
    }
    
    response = requests.get(
        f"{KNOWLEDGE_BOT_URL}/api/v1/auth/validate-user",
        headers=headers
    )
    
    if response.status_code == 200:
        data = response.json()
        print("✅ User validation endpoint working")
        print(f"   Retrieved user data: {data.get('user_data', 'None')}")
        print(f"   Permissions: {data.get('permissions', [])}")
        print(f"   Roles: {data.get('roles', [])}")
    elif response.status_code == 404:
        print("⚠️  User not found in main app (expected if main app not running)")
    elif response.status_code == 500:
        print("❌ Internal server error - check main app connection")
    else:
        print(f"❌ User validation failed: {response.status_code}")

def test_token_validation():
    """Test token validation with main app."""
    print("\n🔍 Testing Token Validation...")
    
    # Test with a sample token (this will fail unless you have a real token)
    sample_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.sample.token"
    
    response = requests.post(
        f"{KNOWLEDGE_BOT_URL}/api/v1/auth/validate-token",
        json=sample_token
    )
    
    if response.status_code == 200:
        print("✅ Token validation successful")
    elif response.status_code == 401:
        print("⚠️  Token validation failed (expected with sample token)")
    else:
        print(f"❌ Token validation error: {response.status_code}")

def test_user_profile_endpoint():
    """Test user profile retrieval."""
    print("\n🔍 Testing User Profile Retrieval...")
    
    user_id = "test-user-123"
    response = requests.get(f"{KNOWLEDGE_BOT_URL}/api/v1/auth/user/{user_id}/profile")
    
    if response.status_code == 200:
        data = response.json()
        print("✅ User profile retrieved successfully")
        print(f"   Profile: {data.get('profile', 'None')}")
    elif response.status_code == 404:
        print("⚠️  User profile not found (expected if main app not running)")
    else:
        print(f"❌ User profile retrieval failed: {response.status_code}")

def test_permission_check():
    """Test permission checking."""
    print("\n🔍 Testing Permission Check...")
    
    user_id = "test-user-123"
    permission = "faq:edit"
    
    response = requests.get(
        f"{KNOWLEDGE_BOT_URL}/api/v1/auth/user/{user_id}/permissions/{permission}"
    )
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Permission check successful")
        print(f"   User {user_id} has permission '{permission}': {data.get('has_permission')}")
    else:
        print(f"❌ Permission check failed: {response.status_code}")

def test_role_check():
    """Test role checking."""
    print("\n🔍 Testing Role Check...")
    
    user_id = "test-user-123"
    role = "ADMIN"
    
    response = requests.get(
        f"{KNOWLEDGE_BOT_URL}/api/v1/auth/user/{user_id}/roles/{role}"
    )
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Role check successful")
        print(f"   User {user_id} has role '{role}': {data.get('has_role')}")
    else:
        print(f"❌ Role check failed: {response.status_code}")

def test_activity_logging():
    """Test activity logging."""
    print("\n🔍 Testing Activity Logging...")
    
    user_id = "test-user-123"
    activity_data = {
        "activity": "test_activity",
        "details": {
            "test": True,
            "timestamp": datetime.utcnow().isoformat()
        }
    }
    
    response = requests.post(
        f"{KNOWLEDGE_BOT_URL}/api/v1/auth/user/{user_id}/activity",
        json=activity_data
    )
    
    if response.status_code == 200:
        print("✅ Activity logging successful")
    else:
        print(f"❌ Activity logging failed: {response.status_code}")

def test_faq_creation_with_validation():
    """Test FAQ creation with user validation."""
    print("\n🔍 Testing FAQ Creation with User Validation...")
    
    headers = {
        "X-User-ID": "test-user-123",
        "X-User-Email": "<EMAIL>",
        "X-User-Roles": "USER,FAQ_EDITOR"
    }
    
    faq_data = {
        "question": "Test question from API integration test",
        "answer": "Test answer from API integration test",
        "category": "test",
        "priority": 1
    }
    
    response = requests.post(
        f"{KNOWLEDGE_BOT_URL}/api/v1/faq/",
        headers=headers,
        json=faq_data
    )
    
    if response.status_code == 201:
        print("✅ FAQ creation with validation successful")
        data = response.json()
        print(f"   Created FAQ ID: {data.get('id')}")
    elif response.status_code == 401:
        print("⚠️  FAQ creation failed - user validation failed (expected if main app not running)")
    elif response.status_code == 403:
        print("⚠️  FAQ creation failed - insufficient permissions")
    else:
        print(f"❌ FAQ creation failed: {response.status_code}")

def test_main_app_connectivity():
    """Test if main app is reachable."""
    print("\n🔍 Testing Main App Connectivity...")
    
    try:
        response = requests.get(f"{MAIN_APP_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Main NestJS app is reachable")
            return True
        else:
            print(f"⚠️  Main app responded with status: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to main app: {e}")
        return False

def test_knowledge_bot_health():
    """Test if knowledge bot is running."""
    print("🔍 Testing Knowledge Bot Health...")
    
    try:
        response = requests.get(f"{KNOWLEDGE_BOT_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Knowledge Bot is running")
            return True
        else:
            print(f"❌ Knowledge Bot health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to Knowledge Bot: {e}")
        return False

def main():
    """Run all API integration tests."""
    print("🚀 Knowledge Bot API Integration Tests")
    print("=" * 50)
    print("Testing API calls to main NestJS application")
    print("=" * 50)
    
    # Check if services are running
    kb_running = test_knowledge_bot_health()
    if not kb_running:
        print("\n❌ Knowledge Bot is not running. Please start it first.")
        sys.exit(1)
    
    main_app_running = test_main_app_connectivity()
    if not main_app_running:
        print("\n⚠️  Main NestJS app is not reachable.")
        print("Some tests will fail, but this shows the integration points.")
    
    print("\n" + "=" * 50)
    print("Running API Integration Tests...")
    print("=" * 50)
    
    # Run all tests
    test_user_validation_endpoint()
    test_token_validation()
    test_user_profile_endpoint()
    test_permission_check()
    test_role_check()
    test_activity_logging()
    test_faq_creation_with_validation()
    
    print("\n" + "=" * 50)
    print("🏁 API Integration tests completed!")
    print("\nNext Steps:")
    if not main_app_running:
        print("1. ❗ Implement the required API endpoints in your main NestJS app")
        print("2. ❗ See API_INTEGRATION.md for detailed endpoint specifications")
        print("3. ❗ Start your main NestJS application")
        print("4. ❗ Re-run this test to verify integration")
    else:
        print("1. ✅ Main app is running")
        print("2. ❗ Check if all required endpoints are implemented")
        print("3. ❗ Verify API responses match expected format")
    
    print("\nFor detailed API specifications, see:")
    print("- API_INTEGRATION.md")
    print("- AUTHENTICATION.md")

if __name__ == "__main__":
    main()
