#!/usr/bin/env python3
"""
Test script to verify that the AI bot uses previous conversation context.
This script simulates a conversation and checks if the bot remembers previous context.
"""

import os
import sys
import uuid
from datetime import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.RagChain.rag_service import RAGService
from src.RagChain.chat_service import ChatService
from src.schemas import RAGQ<PERSON>y

def test_conversation_context():
    """Test that the AI bot uses previous conversation context."""
    
    print("🤖 Testing AI Bot Conversation Context")
    print("=" * 50)
    
    # Initialize services
    rag_service = RAGService()
    chat_service = ChatService()
    
    # Create a test user session
    test_user_id = str(uuid.uuid4())
    print(f"📝 Created test user: {test_user_id}")
    
    # Create a chat session
    chat_session = chat_service.create_chat_session(test_user_id, "Test Conversation Context")
    session_id = chat_session.id
    print(f"💬 Created chat session: {session_id}")
    
    # Test conversation flow
    conversations = [
        {
            "message": "Hi, my name is <PERSON> and I work in the IT department.",
            "expected_context": "Should remember the user's name and department"
        },
        {
            "message": "I'm having trouble with my laptop password.",
            "expected_context": "Should remember John from IT department and the password issue"
        },
        {
            "message": "What was my name again?",
            "expected_context": "Should remember that the user said their name was John"
        }
    ]
    
    print("\n🔄 Starting conversation simulation...")
    
    for i, conv in enumerate(conversations, 1):
        print(f"\n--- Message {i} ---")
        print(f"User: {conv['message']}")
        
        # Create RAG query
        rag_query = RAGQuery(
            query=conv['message'],
            max_results=5,
            similarity_threshold=0.7
        )
        
        # Get response with conversation context
        response = rag_service.query_rag(rag_query, session_id)
        
        print(f"Bot: {response.answer}")
        print(f"Source: {response.response_source}")
        print(f"Confidence: {response.confidence_score:.2f}")
        print(f"Expected Context: {conv['expected_context']}")
        
        # Store the interaction
        chat_service.store_chat_interaction(
            user_id=test_user_id,
            message=conv['message'],
            response=response.answer,
            confidence_score=response.confidence_score,
            retrieved_faqs=[str(faq.faq.id) for faq in response.retrieved_faqs],
            response_source=response.response_source
        )
        
        print("✅ Interaction stored")
    
    # Test conversation context retrieval
    print(f"\n📚 Testing conversation context retrieval...")
    context = chat_service.get_recent_conversation_context(session_id, limit=5)
    print(f"Retrieved {len(context)} conversation exchanges:")
    
    for j, exchange in enumerate(context, 1):
        print(f"\nExchange {j}:")
        print(f"  User: {exchange['user_message']}")
        print(f"  Bot: {exchange['assistant_response'][:100]}...")
        print(f"  Timestamp: {exchange['timestamp']}")
    
    # Test context formatting
    print(f"\n🎨 Testing context formatting...")
    formatted_context = rag_service.format_conversation_context(context)
    print("Formatted context:")
    print(formatted_context[:500] + "..." if len(formatted_context) > 500 else formatted_context)
    
    print(f"\n✅ Test completed successfully!")
    print(f"Session ID: {session_id}")
    print(f"User ID: {test_user_id}")
    
    return session_id, test_user_id

if __name__ == "__main__":
    try:
        session_id, user_id = test_conversation_context()
        print(f"\n🎉 All tests passed! The AI bot now uses conversation context.")
        print(f"You can check the database for session {session_id} and user {user_id}")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
