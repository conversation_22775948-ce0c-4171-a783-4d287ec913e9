# Simplified Authentication System Documentation

## Overview

The Knowledge Bot microservice uses a **simplified authentication approach** where **ALL authentication logic is handled by the main NestJS application**. The microservice receives user context via HTTP headers and does not maintain any authentication state.

This approach eliminates the need to:
- Duplicate JWT validation logic
- Maintain authentication secrets in multiple places
- Keep authentication libraries in sync
- Handle token expiration and refresh

## Architecture

### Header-Based Authentication

The main NestJS application handles all authentication and passes user context to the microservice via HTTP headers:

```
X-User-ID: user-uuid-here
X-User-Email: <EMAIL>
X-User-Roles: USER,ADMIN
X-Anonymous-Session-ID: session-uuid-here (for anonymous users)
```

### Authentication Flow

1. **User authenticates with main NestJS app** (login, JWT validation, etc.)
2. **Main app makes requests to knowledge bot** with user context in headers
3. **Knowledge bot extracts user info** from headers (no token validation needed)
4. **Knowledge bot processes request** with user context

## User Types

### 1. Authenticated Users (via Main App)

**Features:**
- Full access to all FAQ management operations
- Persistent chat history stored in database
- User-specific chat sessions
- Advanced RAG query features

**How it works:**
- User authenticates with main NestJS application
- Main app includes user context in headers when calling knowledge bot
- Knowledge bot extracts user info from headers (no token validation needed)

**Example Request from Main App:**
```bash
curl -X POST "http://localhost:5000/api/v1/faq/" \
  -H "X-User-ID: user-uuid-123" \
  -H "X-User-Email: <EMAIL>" \
  -H "X-User-Roles: USER,ADMIN" \
  -H "Content-Type: application/json" \
  -d '{"question": "How to reset password?", "answer": "Click forgot password..."}'
```

### 2. Anonymous Users

**Features:**
- Can use RAG system for asking questions
- Receive responses from FAQ knowledge base
- Temporary session-based interactions
- No persistent chat history

**Limitations:**
- Cannot create, update, or delete FAQs
- Cannot access advanced RAG features
- Chat history is temporary and session-based only
- Sessions expire after 24 hours

**Session Management:**
Anonymous users can create a session for continuity:

```bash
# Create anonymous session
curl -X POST "http://localhost:5000/api/v1/auth/anonymous-session"

# Use session in subsequent requests
curl -X POST "http://localhost:5000/api/v1/chat/" \
  -H "X-Anonymous-Session-ID: session-uuid" \
  -H "Content-Type: application/json" \
  -d '"How do I reset my password?"'
```

### 3. Optional API Key Protection

**Features:**
- Simple API key for basic protection (optional)
- Can be enabled/disabled via environment variable
- Protects against unauthorized direct access

**Configuration:**
```bash
# Enable API key protection
KNOWLEDGE_BOT_API_KEY="your-secret-api-key"

# Disable API key protection (default)
KNOWLEDGE_BOT_API_KEY=""
```

## API Endpoints

### Authentication Endpoints

| Endpoint | Method | Auth Required | Description |
|----------|--------|---------------|-------------|
| `/api/v1/auth/user-context` | GET | Headers | Show user context from headers |
| `/api/v1/auth/anonymous-session` | POST | No | Create anonymous session |
| `/api/v1/auth/anonymous-session/{id}` | GET | No | Get anonymous session info |
| `/api/v1/auth/anonymous-sessions/cleanup` | DELETE | Optional API Key | Clean up expired sessions |

### Chat Endpoints

| Endpoint | Method | Auth Required | Description |
|----------|--------|---------------|-------------|
| `/api/v1/chat/` | POST | Optional | Chat with RAG system (supports anonymous) |
| `/api/v1/chat/query` | POST | Headers | Advanced RAG query (requires user context) |
| `/api/v1/chat/anonymous-history/{id}` | GET | No | Get anonymous session history |

### FAQ Endpoints

| Endpoint | Method | Auth Required | Description |
|----------|--------|---------------|-------------|
| `/api/v1/faq/` | GET | No | List FAQs (public) |
| `/api/v1/faq/` | POST | Headers | Create FAQ (requires user context) |
| `/api/v1/faq/{id}` | PUT | Headers | Update FAQ (requires user context) |
| `/api/v1/faq/{id}` | DELETE | Headers | Delete FAQ (requires user context) |
| `/api/v1/faq/bulk` | POST | Headers | Bulk create FAQs (requires user context) |

**Auth Required Legend:**
- **No**: Public endpoint, no authentication needed
- **Headers**: Requires user context headers from main app
- **Optional**: Supports both authenticated (headers) and anonymous users
- **Optional API Key**: Protected by API key if configured

## Configuration

### Environment Variables

```bash
# Main Application URL
MAIN_APP_URL="http://localhost:3000"

# Optional API Key for basic protection (leave empty to disable)
KNOWLEDGE_BOT_API_KEY=""
```

### Integration with Main NestJS App

1. **Header-Based Context**: Main app passes user context via HTTP headers
2. **No Shared Secrets**: No need to synchronize JWT secrets or authentication logic
3. **Simplified Deployment**: Knowledge bot has minimal authentication dependencies

### Required Headers from Main App

When the main NestJS application calls the knowledge bot for authenticated operations, it should include:

```
X-User-ID: user-uuid-here          # Required for user-specific operations
X-User-Email: <EMAIL>     # Optional, for logging/analytics
X-User-Roles: USER,ADMIN           # Optional, for role-based features
```

## Security Considerations

1. **JWT Secret**: Keep JWT secret secure and synchronized between applications
2. **Service API Keys**: Use strong, unique API keys for service-to-service communication
3. **Token Expiration**: Respect token expiration times
4. **HTTPS**: Use HTTPS in production for all communications
5. **Rate Limiting**: Implement rate limiting for anonymous users

## Error Handling

### Common Error Responses

```json
// Invalid or expired token
{
  "detail": "Token has expired",
  "status_code": 401
}

// Missing authentication
{
  "detail": "Authentication required",
  "status_code": 401
}

// Invalid service credentials
{
  "detail": "Invalid service credentials",
  "status_code": 403
}

// Anonymous session not found
{
  "detail": "Anonymous session not found or expired",
  "status_code": 404
}
```

## Usage Examples

### Authenticated User Flow

```python
import requests

# Login to main app (handled by main NestJS app)
login_response = requests.post("http://localhost:3000/auth/login", {
    "email": "<EMAIL>",
    "password": "password"
})
token = login_response.json()["token"]

# Use token with knowledge bot
headers = {"Authorization": f"Bearer {token}"}

# Create FAQ
faq_response = requests.post(
    "http://localhost:5000/api/v1/faq/",
    headers=headers,
    json={
        "question": "How to reset password?",
        "answer": "Click the forgot password link..."
    }
)

# Chat with RAG system
chat_response = requests.post(
    "http://localhost:5000/api/v1/chat/",
    headers=headers,
    json="How do I reset my password?"
)
```

### Anonymous User Flow

```python
import requests

# Create anonymous session
session_response = requests.post(
    "http://localhost:5000/api/v1/auth/anonymous-session"
)
session_id = session_response.json()["session_id"]

# Chat with session continuity
headers = {"X-Anonymous-Session-ID": session_id}
chat_response = requests.post(
    "http://localhost:5000/api/v1/chat/",
    headers=headers,
    json="How do I reset my password?"
)

# Get session history
history_response = requests.get(
    f"http://localhost:5000/api/v1/chat/anonymous-history/{session_id}"
)
```

## Monitoring and Maintenance

### Session Cleanup

Anonymous sessions are automatically cleaned up after 24 hours. For manual cleanup:

```bash
curl -X DELETE "http://localhost:5000/api/v1/auth/anonymous-sessions/cleanup" \
  -H "X-Service-API-Key: your-service-key" \
  -H "X-Service-Name: main-nestjs-app"
```

### Health Checks

Monitor authentication system health:

```bash
# Verify token validation is working
curl -X GET "http://localhost:5000/api/v1/auth/verify" \
  -H "Authorization: Bearer valid-token"

# Check RAG system health
curl -X GET "http://localhost:5000/api/v1/chat/health"
```
